<?php $__env->startSection('title', 'Users - Vertigo AMS'); ?>

<?php $__env->startSection('content'); ?>
<div class="min-h-screen bg-gray-50">
    <!-- Page Header -->
    <div class="bg-white border-b border-gray-200">
        <div class="px-4 sm:px-6 lg:px-8 py-6">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div class="flex-1 min-w-0">
                    <h1 class="text-2xl font-bold text-gray-900 sm:text-3xl">Users Management</h1>
                    <p class="mt-1 text-sm text-gray-500">Manage system users, roles, and permissions</p>
                </div>
                <div class="mt-4 sm:mt-0 sm:ml-4 flex flex-col sm:flex-row gap-3">
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('create', App\Models\User::class)): ?>
                    <a href="<?php echo e(route('admin.modernized.users.create')); ?>"
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white gradient-primary hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200">
                        <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Add User
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="px-1 sm:px-6 lg:px-1 py-4">
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <!-- Search and Filters -->
            <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900">Users List</h3>
                        <p class="mt-1 text-sm text-gray-500">A list of all users in the system including their roles and status.</p>
                    </div>

                    <!-- Search and Filter Controls -->
                    <div class="flex flex-col sm:flex-row gap-3 lg:w-auto">
                        <!-- Search Form -->
                        <form method="GET" class="flex items-center">
                            <!-- Preserve existing filters -->
                            <?php if(request()->role_id): ?>
                                <input type="hidden" name="role_id" value="<?php echo e(request()->role_id); ?>">
                            <?php endif; ?>

                            <div class="relative flex-1 min-w-0">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <input type="text"
                                       name="search"
                                       value="<?php echo e(request()->search); ?>"
                                       placeholder="Search users..."
                                       class="block w-full sm:w-64 pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                            </div>
                            <button type="submit"
                                    class="ml-2 inline-flex items-center px-4 py-2 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Search
                            </button>
                        </form>

                        <!-- Role Filter -->
                        <form method="GET" id="filter" class="flex items-center">
                            <!-- Preserve search term -->
                            <?php if(request()->search): ?>
                                <input type="hidden" name="search" value="<?php echo e(request()->search); ?>">
                            <?php endif; ?>

                            <select class="block w-full sm:w-48 px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-sm"
                                    name="role_id" onchange="this.form.submit()" autocomplete="off">
                                <option value="">All Roles</option>
                                <?php $__currentLoopData = App\Models\Role::get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option <?php if(request()->role_id == $role->id): ?> selected <?php endif; ?> value="<?php echo e($role->id); ?>">
                                    <?php echo e($role->name ?? ''); ?>

                                </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </form>

                        <!-- Clear Filters -->
                        <?php if(request()->search || request()->role_id): ?>
                        <a href="<?php echo e(route('admin.modernized.users.index')); ?>"
                           class="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Clear
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Table -->
            <div class="overflow-x-auto">
                <table class="table js-datatable min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                User
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Email
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Roles
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Branch
                            </th>
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php $__empty_1 = true; $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <img class="h-10 w-10 rounded-full object-cover border-2 border-gray-200" 
                                             src="<?php echo e($user->image ?? 'https://ui-avatars.com/api/?name=' . urlencode($user->name) . '&color=0068ff&background=f0f9ff'); ?>" 
                                             alt="<?php echo e($user->name); ?>">
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo e($user->name ?? '-'); ?>

                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo e($user->phone ?? '-'); ?>

                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo e($user->email ?? '-'); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-wrap gap-1">
                                    <?php $__currentLoopData = $user->roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <?php echo e($role->name ?? '-'); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900"><?php echo e($user->branch->name ?? '-'); ?></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-center">
                                <div class="flex items-center justify-center space-x-2">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view', $user)): ?>
                                    <a href="<?php echo e(route('admin.modernized.users.show', $user)); ?>" 
                                       class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                        <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        View
                                    </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $user)): ?>
                                    <a href="<?php echo e(route('admin.modernized.users.edit', $user)); ?>" 
                                       class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md text-xs font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                        <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                        </svg>
                                        Edit
                                    </a>
                                    <?php endif; ?>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('delete', $user)): ?>
                                    <form action="<?php echo e(route('admin.modernized.users.destroy', $user)); ?>" 
                                          method="POST" 
                                          onsubmit="return confirm('Are you sure you want to delete this user?')"
                                          class="inline-block">
                                        <?php echo csrf_field(); ?> <?php echo method_field('DELETE'); ?>
                                        <button type="submit" 
                                                class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md text-xs font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                                            <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                            </svg>
                                            Delete
                                        </button>
                                    </form>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center">
                                <div class="flex flex-col items-center">
                                    <svg class="h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                    <h3 class="text-lg font-medium text-gray-900 mb-2">No users found</h3>
                                    <p class="text-gray-500">Get started by creating a new user.</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>

                

            </div>

            <!-- Pagination and Export Options -->
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <!-- Export Options -->
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-700 font-medium">Export:</span>
                        <div class="flex space-x-1">
                            <button id="export-copy"
                                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                Copy
                            </button>
                            <button id="export-csv"
                                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                CSV
                            </button>
                            <button id="export-excel"
                                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                Excel
                            </button>
                            <button id="export-pdf"
                                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                PDF
                            </button>
                            <button id="export-print"
                                    class="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                                </svg>
                                Print
                            </button>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="flex justify-end">
                        <?php echo e($users->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    console.log('Users page loaded');

    // Initialize DataTable with hidden built-in controls
    // Since the dataTableBtn function has issues with custom options,
    // we'll initialize it normally and then hide the unwanted elements
    dataTableBtn('.js-datatable', null, [0, 'asc']);

    // Hide the built-in DataTable controls after initialization
    setTimeout(function() {
        // Hide the built-in search box
        $('.dataTables_filter').hide();

        // Hide the built-in pagination
        $('.dataTables_paginate').hide();

        // Hide the built-in info text
        $('.dataTables_info').hide();

        // Hide the built-in length menu
        $('.dataTables_length').hide();

        // Hide the built-in export buttons container
        $('.dt-buttons').hide();

        console.log('DataTable built-in controls hidden');
    }, 100);

    // Wait for DataTable to be fully initialized
    setTimeout(function() {
        try {
            // Debug: Check what DataTable buttons are available
            console.log('Available DataTable buttons:', $('.dt-buttons button').length);
            $('.dt-buttons button').each(function(index) {
                console.log('Button ' + index + ':', $(this).text(), $(this).attr('class'));
            });

            // Try to get DataTable instance
            let datatable = null;

            // Method 1: Try HSCore components
            if (typeof HSCore !== 'undefined' && HSCore.components && HSCore.components.HSDatatables) {
                datatable = HSCore.components.HSDatatables.getItem(0);
                console.log('HSCore DataTable instance:', datatable);
            }

            // Method 2: Try direct jQuery DataTable access
            if (!datatable && $.fn.DataTable) {
                const tableElement = $('.js-datatable').get(0);
                if (tableElement && $.fn.DataTable.isDataTable(tableElement)) {
                    datatable = $('.js-datatable').DataTable();
                    console.log('jQuery DataTable instance:', datatable);
                }
            }

            if (datatable) {
                // Wire up export buttons using button indices
                $('#export-copy').off('click').on('click', function() {
                    console.log('Copy button clicked');
                    try {
                        datatable.button(0).trigger();
                    } catch (e) {
                        console.error('Copy export failed:', e);
                        // Fallback: try clicking the actual button
                        $('.dt-buttons button:contains("Copy")').click();
                    }
                });

                $('#export-csv').off('click').on('click', function() {
                    console.log('CSV button clicked');
                    try {
                        datatable.button(1).trigger();
                    } catch (e) {
                        console.error('CSV export failed:', e);
                        $('.dt-buttons button:contains("CSV")').click();
                    }
                });

                $('#export-excel').off('click').on('click', function() {
                    console.log('Excel button clicked');
                    try {
                        datatable.button(2).trigger();
                    } catch (e) {
                        console.error('Excel export failed:', e);
                        $('.dt-buttons button:contains("Excel")').click();
                    }
                });

                $('#export-pdf').off('click').on('click', function() {
                    console.log('PDF button clicked');
                    try {
                        datatable.button(3).trigger();
                    } catch (e) {
                        console.error('PDF export failed:', e);
                        $('.dt-buttons button:contains("PDF")').click();
                    }
                });

                $('#export-print').off('click').on('click', function() {
                    console.log('Print button clicked');
                    try {
                        datatable.button(4).trigger();
                    } catch (e) {
                        console.error('Print export failed:', e);
                        $('.dt-buttons button:contains("Print")').click();
                    }
                });

                console.log('Export buttons wired up successfully');
            } else {
                console.warn('DataTable instance not found, using fallback method');

                // Fallback: Try to find and click the actual DataTable buttons
                $('#export-copy').off('click').on('click', function() {
                    console.log('Fallback: Copy button clicked');
                    $('.dt-buttons button:contains("Copy")').click();
                });
                $('#export-csv').off('click').on('click', function() {
                    console.log('Fallback: CSV button clicked');
                    $('.dt-buttons button:contains("CSV")').click();
                });
                $('#export-excel').off('click').on('click', function() {
                    console.log('Fallback: Excel button clicked');
                    $('.dt-buttons button:contains("Excel")').click();
                });
                $('#export-pdf').off('click').on('click', function() {
                    console.log('Fallback: PDF button clicked');
                    $('.dt-buttons button:contains("PDF")').click();
                });
                $('#export-print').off('click').on('click', function() {
                    console.log('Fallback: Print button clicked');
                    $('.dt-buttons button:contains("Print")').click();
                });
            }
        } catch (error) {
            console.error('Error setting up export buttons:', error);
        }
    }, 1500);
});
</script>
<?php $__env->stopPush(); ?>



<?php echo $__env->make('layouts.modernized-admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/admin/modernized-users/index.blade.php ENDPATH**/ ?>