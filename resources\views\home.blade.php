
@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="container-fluid">
    <!-- Welcome Banner -->
    <div class="position-relative">
        <div class="container py-4 position-relative">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">Welcome to Vertigo AMS</h2>
                    <p class="mb-0">Auction Management System</p>
                </div>
                
                <div class="col-md-4 d-flex justify-content-md-end">
                    <form class="d-flex" method="GET">
                        <input type="hidden" name="from" value="{{ request()->from }}">
                        <input type="hidden" name="to" value="{{ request()->to }}">

                        <button id="js-daterangepicker-predefined" type="button" class="btn btn-sm btn-light me-2">
                            <i class="bi-calendar-week me-1"></i>
                            <span class="js-daterangepicker-predefined-preview"></span>
                        </button>

                        <button type="submit" class="btn btn-sm bg-dark text-white">
                            <i class="bi-filter me-1"></i> Apply Filter
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- End Welcome Banner -->

    <!-- Content -->
    <div class="navbar-sidebar-aside-content">
        <!-- Stats Cards Section -->
        <div class="row mb-4">
        @can('view-any', App\Models\Order::class)
            <!-- Card -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <a class="card h-100 text-center bg-white border" href="/transactions">
                    <div class="card-body">
                        <div class="mb-2">
                            <i class="bi-currency-dollar fs-2 text-dark"></i>
                        </div>
                        <h2 class="card-title h2 text-dark">
                            {{ _money( Facades\App\Cache\Repo::getTotalSales() ) }}
                        </h2>
                        <h6 class="card-text text-muted">Total Sales</h6>
                    </div>
                </a>
            </div>
            <!-- End Card -->
            @endcan

            <!-- Card -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <a class="card h-100 text-center bg-white border" href="/users">
                    <div class="card-body">
                        <div class="mb-2">
                            <i class="bi-people fs-2 text-dark"></i>
                        </div>
                        <h2 class="card-title h2 text-dark">
                            {{ _number( Facades\App\Cache\Repo::getStaffCursor()->count() )}}
                        </h2>
                        <h6 class="card-text text-muted">Staff</h6>
                    </div>
                </a>
            </div>
            <!-- End Card -->

            @can('view-any', App\Models\Customer::class)
            <!-- Card -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <a class="card h-100 text-center bg-white border" href="/users">
                    <div class="card-body">
                        <div class="mb-2">
                            <i class="bi-person-badge fs-2 text-dark"></i>
                        </div>
                        <h2 class="card-title h2 text-dark">
                            {{ _number( Facades\App\Cache\Repo::getCustomerCursor()->count() )}}
                        </h2>
                        <h6 class="card-text text-muted">Customers</h6>
                    </div>
                </a>
            </div>
            <!-- End Card -->
            @endcan

            @can('view-any', App\Models\AuctionType::class)
            <!-- Card -->
            <div class="col-sm-6 col-lg-3 mb-3">
                <a class="card h-100 text-center bg-white border" href="/auction-listing">
                    <div class="card-body">
                        <div class="mb-2">
                            <i class="bi-list-check fs-2 text-dark"></i>
                        </div>
                        <h2 class="card-title h2 text-dark">
                            {{ _number( App\Models\AuctionType::where('type', 'live')->count() )}}
                        </h2>
                        <h6 class="card-text text-muted">Auction Listings</h6>
                    </div>
                </a>
            </div>
            <!-- End Card -->
            @endcan

            
        </div>
        <!-- End Stats Cards Section -->

        <!-- Monthly Sales Chart -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border">
                    <div class="card-header">
                        <div class="d-flex align-items-center">
                            <i class="bi-graph-up-arrow fs-5 text-dark me-2"></i>
                            <h5 class="card-header-title mb-0">Monthly Sales Performance</h5>
                        </div>
                    </div>
                    <!-- Body -->
                    <div class="card-body">
                        <div class="row">
                            <div class="col-lg-9 mb-4 mb-lg-0">
                                <!-- Bar Chart -->
                                <div class="chartjs-custom">
                                    <canvas id="ecommerce-sales" class="js-chart" style="height: 18rem;" data-hs-chartjs-options='{
                                        "type": "bar",
                                        "data": {
                                            "labels": ["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct", "Nov", "Dec"],
                                            "datasets": [{
                                                "data": @json(Facades\App\Cache\Repo::getAnnualSales()),
                                                "backgroundColor": "#6c757d",
                                                "hoverBackgroundColor": "#343a40",
                                                "borderColor": "#6c757d",
                                                "maxBarThickness": "12"
                                            }]
                                        },
                                        "options": {
                                            "scales": {
                                                "y": {
                                                    "grid": {
                                                        "color": "#e7eaf3",
                                                        "drawBorder": false,
                                                        "zeroLineColor": "#e7eaf3"
                                                    },
                                                    "ticks": {
                                                        "beginAtZero": true,
                                                        "stepSize": 100,
                                                        "color": "#6c757d",
                                                        "font": {
                                                            "size": 12,
                                                            "family": "Open Sans, sans-serif"
                                                        },
                                                        "padding": 10
                                                    }
                                                },
                                                "x": {
                                                    "grid": {
                                                        "display": false,
                                                        "drawBorder": false
                                                    },
                                                    "ticks": {
                                                        "color": "#6c757d",
                                                        "font": {
                                                            "size": 12,
                                                            "family": "Open Sans, sans-serif"
                                                        },
                                                        "padding": 5
                                                    },
                                                    "categoryPercentage": 0.6,
                                                    "maxBarThickness": "12"
                                                }
                                            },
                                            "cornerRadius": 2,
                                            "plugins": {
                                                "tooltip": {
                                                    "hasIndicator": true,
                                                    "mode": "index",
                                                    "intersect": false
                                                }
                                            },
                                            "hover": {
                                                "mode": "nearest",
                                                "intersect": true
                                            }
                                        }
                                    }'></canvas>
                                </div>
                                <!-- End Bar Chart -->

                                <div class="row justify-content-center mt-3">
                                    <div class="col-auto">
                                        <span class="legend-indicator bg-secondary"></span> Revenue
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-3">
                                <div class="card h-100 border">
                                    <div class="card-body text-center">
                                        <div class="mb-3">
                                            <i class="bi-cash-coin fs-3 text-dark"></i>
                                        </div>
                                        <h6 class="card-subtitle text-muted">Total Annual Revenue</h6>
                                        <span class="d-block h2 text-dark mb-1">
                                            {{ _money( array_sum( Facades\App\Cache\Repo::getAnnualSales() ) ) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- End Body -->
                </div>
            </div>
        </div>
        <!-- End Monthly Sales Chart -->

        <!-- Auction Categories Section -->
        @foreach(App\Models\AuctionType::get() as $cat)
            @if($cat->items()->count())
                <div class="mb-4">
                    <div class="card border">
                        <div class="card-header">
                            <div class="d-flex align-items-center">
                                <i class="bi-tag fs-5 text-dark me-2"></i>
                                <h5 class="card-header-title mb-0">{{ $cat->name ?? 'Auction Items' }}</h5>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-xl-5 g-3">
                                @foreach($cat->items as $item)
                                    <div class="col">
                                        <a class="card h-100 border" href="/items/{{ $item->id ?? '' }}">
                                            <div class="position-relative">
                                                <img class="card-img-top" src="{{ $item->cropped ?? '' }}" alt="{{ $item->name ?? 'Item Image' }}">

                                                @if($item->closed_by)
                                                    <div class="position-absolute top-0 end-0 p-2">
                                                        <span class="badge bg-secondary">Sold</span>
                                                    </div>
                                                @endif
                                            </div>

                                            <div class="card-body">
                                                <h6 class="card-title text-truncate">{{ $item->name ?? '' }}</h6>
                                                <p class="card-text small text-muted mb-1">
                                                    <span class="badge bg-light text-dark border">{{ $item->auctionType->name ?? '' }}</span>
                                                </p>
                                                <p class="card-text small text-muted text-truncate">
                                                    {{ $item->description ?? '' }}
                                                </p>
                                            </div>
                                            <div class="card-footer bg-transparent border-top">
                                                <small class="text-muted">View Details <i class="bi-chevron-right small ms-1"></i></small>
                                            </div>
                                        </a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        @endforeach
        <!-- End Auction Categories Section -->
    </div>
</div>
@endsection
