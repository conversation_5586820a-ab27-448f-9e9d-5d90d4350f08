@extends('layouts.app')

@section('content')
<!-- Content -->
<div class="content container-fluid">
    <div class="row align-items-center mb-4">
        <div class="col-sm-6">
            <h1 class="page-header-title">Users</h1>
        </div>
        <div class="col-sm-6">
            <div class="d-flex justify-content-end align-items-center">
                <form method="GET" id="filter" class="d-flex align-items-center me-3 mb-0">
                    <div class="me-2" style="min-width: 200px;">
                        <select class="form-select form-select-sm" name="role_id" onchange="this.form.submit()" autocomplete="off">
                            <option value="0">All Roles</option>
                            @foreach(App\Models\Role::get() as $role)
                            <option @if(request()->role_id == $role->id) selected @endif value="{{ $role->id }}">
                                {{ $role->name ?? '' }}
                            </option>
                            @endforeach
                        </select>
                    </div>
                </form>

                @can('create', App\Models\User::class)
                <a href="{{ route('users.create') }}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-1"></i>
                    Add User
                </a>
                @endcan
            </div>
        </div>
    </div>


    <div class="card">
        <div class="card-header">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="card-title mb-0">Users List</h5>
                </div>
            </div>
        </div>


        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table js-datatable table-borderless table-thead-bordered table-nowrap table-align-middle card-table">
                    <thead class="thead-light">
                        <tr>
                            <th class="text-left ps-3">
                                @lang('crud.users.inputs.name')
                            </th>
                            <th class="text-left ps-3">
                                @lang('crud.users.inputs.email')
                            </th>
                            <th class="text-left ps-3">
                                Roles
                            </th>
                            <th class="text-left ps-3">
                                Branch
                            </th>
                            <th class="text-center">
                                @lang('crud.common.actions')
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($users as $user)
                        <tr>
                            <td class="ps-3">
                              <a class="d-flex align-items-center" href="/users/{{ $user->id ?? '-' }}">
                                <div class="flex-shrink-0">
                                  <img class="avatar avatar-lg" src="{{ $user->image ?? '-' }}" alt="Image Description">
                                </div>
                                <div class="flex-grow-1 ms-3">
                                  <h5 class="text-inherit mb-0">
                                      {{ $user->name ?? '-' }}
                                  </h5>
                                  <small>{{ $user->phone ?? '-' }}</small>
                                </div>
                              </a>
                            </td>
                            <td class="ps-3">{{ $user->email ?? '-' }}</td>
                            <td class="ps-3">
                                @foreach( $user->roles as $role)
                                    <span class="btn-soft-info btn btn-sm"> {{ $role->name ?? '-' }} </span>
                                @endforeach
                            </td>
                            <td class="ps-3">{{ $user->branch->name ?? '-' }}</td>

                            <td class="text-center" style="width: 134px;">
                                <div
                                    role="group"
                                    aria-label="Row Actions"
                                    class="btn-group"
                                >
                                    @can('update', $user)
                                    <a href="{{ route('users.edit', $user) }}">
                                        <button type="button" class="btn btn-primary btn-sm m-1">
                                            <i class="bi bi-pencil-square me-1"></i>
                                            Edit
                                        </button>
                                    </a>
                                    @endcan

                                    @can('view', $user)
                                    <a href="{{ route('users.show', $user) }}">
                                        <button type="button" class="btn btn-info btn-sm m-1 text-white">
                                            <i class="bi bi-eye me-1"></i>
                                            View
                                        </button>
                                    </a>
                                    @endcan

                                    @can('delete', $user)
                                    <form
                                        action="{{ route('users.destroy', $user) }}"
                                        method="POST"
                                        onsubmit="return confirm('{{ __('crud.common.are_you_sure') }}')"
                                    >
                                        @csrf @method('DELETE')
                                        <button
                                            type="submit"
                                            class="btn btn-danger btn-sm m-1"
                                        >
                                            <i class="bi bi-trash me-1"></i>
                                            Delete
                                        </button>
                                    </form>
                                    @endcan
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="5" class="text-center">No users found</td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>

                
            </div>
        </div>
    </div>

</div>
@endsection


@push('scripts')
<script>
  $("document").ready(function () {
    // INITIALIZATION OF DATATABLES
    // =======================================================
   dataTableBtn()
  });
</script>


@endpush